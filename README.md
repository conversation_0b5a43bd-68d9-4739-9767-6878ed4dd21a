# Novalead - Professional Telemarketing & Lead Generation Services

A modern, professional website showcasing Novalead's comprehensive telemarketing and lead generation services. Built with React, TypeScript, and Tailwind CSS for optimal performance and user experience.

## 🚀 Features

- **Professional Design**: Clean, modern interface with Novalead brand colors
- **Responsive Layout**: Optimized for desktop, tablet, and mobile devices
- **Service Showcase**: Comprehensive display of all 7 core services
- **Interactive Contact Form**: Professional contact form with validation
- **Smooth Animations**: Engaging user experience with subtle animations
- **SEO Optimized**: Proper meta tags and semantic HTML structure

## 🛠️ Tech Stack

- **React 19** - Modern React with latest features
- **TypeScript** - Type-safe development
- **Tailwind CSS v4** - Utility-first CSS framework
- **Vite** - Fast build tool and development server
- **Lucide React** - Beautiful, customizable icons

## 🎨 Brand Colors

- **Nova Red**: #E94B6A
- **Nova Orange**: #F29C2B
- **Nova Blue**: #6EC1C9
- **Nova Purple**: #7B5CA2
- **Nova Gray**: #A39E9B

## 📋 Services Featured

1. **Lead Generation** - Quality prospect identification and qualification
2. **Cross-selling and Upselling** - Revenue optimization from existing customers
3. **Appointment Scheduling** - Streamlined prospect meeting coordination
4. **Retention Calls** - Customer relationship maintenance and support
5. **Inbound Telemarketing** - Professional incoming call handling
6. **Customer Satisfaction Research** - Feedback collection and analysis
7. **Calendar Management** - Efficient appointment scheduling integration

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd novalead
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Build for Production

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

## 📁 Project Structure

```
src/
├── components/          # React components
│   ├── Header.tsx      # Navigation header
│   ├── Hero.tsx        # Hero section
│   ├── Services.tsx    # Services showcase
│   ├── ServiceCard.tsx # Individual service cards
│   ├── Contact.tsx     # Contact form and info
│   └── Footer.tsx      # Footer component
├── data/
│   └── services.ts     # Service data configuration
├── types/
│   └── index.ts        # TypeScript type definitions
├── App.tsx             # Main application component
├── main.tsx           # Application entry point
└── index.css          # Global styles and brand colors
```

## 🎯 Key Features

### Professional Design System
- Consistent use of Novalead brand colors
- Modern typography and spacing
- Professional layout patterns

### Responsive Design
- Mobile-first approach
- Optimized for all screen sizes
- Touch-friendly interactions

### Performance Optimized
- Fast loading times
- Optimized images and assets
- Efficient component structure

### Accessibility
- Semantic HTML structure
- Proper ARIA labels
- Keyboard navigation support

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Code Style

This project follows modern React and TypeScript best practices:
- Functional components with hooks
- TypeScript for type safety
- Modular component architecture
- Consistent naming conventions

## 📞 Contact

For questions about this website or Novalead's services:

- **Email**: <EMAIL>
- **Phone**: +****************
- **Address**: 123 Business Ave, Suite 100, New York, NY 10001

## 📄 License

This project is proprietary to Novalead. All rights reserved.
