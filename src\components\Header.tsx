import React, { useState } from 'react';
import { Menu, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from './LanguageSwitcher';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { t } = useTranslation();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    setIsMenuOpen(false);
  };

  return (
    <header className="bg-white shadow-lg fixed w-full top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <div className="flex items-center">
            <button
              onClick={scrollToTop}
              className="focus:outline-none focus:ring-2 focus:ring-nova-red focus:ring-opacity-50 rounded-lg"
              aria-label="Scroll to top"
            >
              <img
                src="/Nova-lead-logo.png"
                alt="Novalead - Professional Telemarketing & Lead Generation Services"
                className="h-10 w-auto sm:h-12 hover:scale-105 transition-transform duration-200"
                loading="eager"
              />
            </button>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <button
              onClick={() => scrollToSection('home')}
              className="text-gray-700 hover:text-nova-red transition-colors duration-200 font-medium"
            >
              {t('navigation.home')}
            </button>
            <button
              onClick={() => scrollToSection('services')}
              className="text-gray-700 hover:text-nova-red transition-colors duration-200 font-medium"
            >
              {t('navigation.services')}
            </button>
            <button
              onClick={() => scrollToSection('contact')}
              className="text-gray-700 hover:text-nova-red transition-colors duration-200 font-medium"
            >
              {t('navigation.contact')}
            </button>
          </nav>

          {/* Language Switcher & CTA Button */}
          <div className="hidden md:flex items-center space-x-4">
            <LanguageSwitcher />
            <button
              onClick={() => scrollToSection('contact')}
              className="bg-nova-red text-white px-6 py-2 rounded-lg hover:bg-opacity-90 transition-all duration-200 font-medium shadow-lg hover:shadow-xl"
            >
              {t('navigation.getStarted')}
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="text-gray-700 hover:text-nova-red transition-colors duration-200"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
              <button
                onClick={() => scrollToSection('home')}
                className="block w-full text-left px-3 py-2 text-gray-700 hover:text-nova-red transition-colors duration-200 font-medium"
              >
                {t('navigation.home')}
              </button>
              <button
                onClick={() => scrollToSection('services')}
                className="block w-full text-left px-3 py-2 text-gray-700 hover:text-nova-red transition-colors duration-200 font-medium"
              >
                {t('navigation.services')}
              </button>
              <button
                onClick={() => scrollToSection('contact')}
                className="block w-full text-left px-3 py-2 text-gray-700 hover:text-nova-red transition-colors duration-200 font-medium"
              >
                {t('navigation.contact')}
              </button>
              <div className="px-3 py-2 flex flex-col space-y-2">
                <LanguageSwitcher />
                <button
                  onClick={() => scrollToSection('contact')}
                  className="w-full bg-nova-red text-white px-6 py-2 rounded-lg hover:bg-opacity-90 transition-all duration-200 font-medium"
                >
                  {t('navigation.getStarted')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
