export interface TranslationResources {
  common: {
    navigation: {
      home: string;
      services: string;
      contact: string;
      getStarted: string;
    };
    hero: {
      title: {
        professional: string;
        telemarketing: string;
        leadGeneration: string;
      };
      description: string;
      buttons: {
        getStartedToday: string;
        viewServices: string;
      };
      stats: {
        clientsReached: {
          number: string;
          label: string;
        };
        successRate: {
          number: string;
          label: string;
        };
        support: {
          number: string;
          label: string;
        };
      };
      services: {
        expertCalling: {
          title: string;
          description: string;
        };
        leadQualification: {
          title: string;
          description: string;
        };
        revenueGrowth: {
          title: string;
          description: string;
        };
        support24: {
          title: string;
          description: string;
        };
      };
    };
    services: {
      title: {
        ourProfessional: string;
        services: string;
      };
      description: string;
      cta: {
        title: string;
        description: string;
        buttons: {
          getFreeConsultation: string;
          viewCaseStudies: string;
        };
      };
    };
    serviceCard: {
      keyBenefits: string;
      learnMore: string;
    };
    contact: {
      title: {
        getIn: string;
        touch: string;
      };
      description: string;
      conversation: {
        title: string;
        description: string;
      };
      contactInfo: {
        phone: string;
        email: string;
        office: string;
      };
      businessHours: {
        title: string;
        mondayFriday: string;
        saturday: string;
        sunday: string;
        closed: string;
        timeWeekdays: string;
        timeSaturday: string;
      };
      form: {
        fullName: string;
        emailAddress: string;
        companyName: string;
        message: string;
        placeholders: {
          fullName: string;
          email: string;
          company: string;
          message: string;
        };
        sendMessage: string;
        success: {
          title: string;
          description: string;
        };
      };
    };
    footer: {
      description: string;
      quickLinks: {
        title: string;
        home: string;
        services: string;
        contact: string;
        aboutUs: string;
        caseStudies: string;
      };
      ourServices: {
        title: string;
        leadGeneration: string;
        crossSelling: string;
        appointmentScheduling: string;
        retentionCalls: string;
        inboundTelemarketing: string;
        customerResearch: string;
        calendarManagement: string;
      };
      legal: {
        copyright: string;
        privacyPolicy: string;
        termsOfService: string;
      };
    };
    servicesData: {
      [key: string]: {
        title: string;
        description: string;
        benefits: string[];
      };
    };
  };
}

export type SupportedLanguages = 'en' | 'fr' | 'nl';
