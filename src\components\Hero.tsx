import React from 'react';
import { ArrowR<PERSON>, Phone, Users, TrendingUp } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const Hero: React.FC = () => {
  const { t } = useTranslation();

  const scrollToServices = () => {
    const element = document.getElementById('services');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const scrollToContact = () => {
    const element = document.getElementById('contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="pt-20 pb-16 bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Content */}
          <div className="animate-fade-in-up">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
              {t('hero.title.professional')}{' '}
              <span className="gradient-nova-text">
                {t('hero.title.telemarketing')}
              </span>{' '}
              {t('hero.title.leadGeneration')}
            </h1>

            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              {t('hero.description')}
            </p>

            <div className="flex flex-col sm:flex-row gap-4 mb-12">
              <button
                onClick={scrollToContact}
                className="bg-nova-red text-white px-8 py-4 rounded-lg hover:bg-opacity-90 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl flex items-center justify-center group"
              >
                {t('hero.buttons.getStartedToday')}
                <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform duration-200" size={20} />
              </button>

              <button
                onClick={scrollToServices}
                className="border-2 border-nova-red text-nova-red px-8 py-4 rounded-lg hover:bg-nova-red hover:text-white transition-all duration-200 font-semibold"
              >
                {t('hero.buttons.viewServices')}
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-nova-red mb-2">{t('hero.stats.clientsReached.number')}</div>
                <div className="text-gray-600 text-sm">{t('hero.stats.clientsReached.label')}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-nova-orange mb-2">{t('hero.stats.successRate.number')}</div>
                <div className="text-gray-600 text-sm">{t('hero.stats.successRate.label')}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-nova-blue mb-2">{t('hero.stats.support.number')}</div>
                <div className="text-gray-600 text-sm">{t('hero.stats.support.label')}</div>
              </div>
            </div>
          </div>

          {/* Right Column - Visual Elements */}
          <div className="relative">
            <div className="gradient-nova-subtle rounded-2xl p-8 backdrop-blur-sm">
              <div className="grid grid-cols-2 gap-6">
                {/* Service Icons */}
                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
                  <Phone className="text-nova-red mb-4" size={32} />
                  <h3 className="font-semibold text-gray-900 mb-2">{t('hero.services.expertCalling.title')}</h3>
                  <p className="text-gray-600 text-sm">{t('hero.services.expertCalling.description')}</p>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
                  <Users className="text-nova-blue mb-4" size={32} />
                  <h3 className="font-semibold text-gray-900 mb-2">{t('hero.services.leadQualification.title')}</h3>
                  <p className="text-gray-600 text-sm">{t('hero.services.leadQualification.description')}</p>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
                  <TrendingUp className="text-nova-orange mb-4" size={32} />
                  <h3 className="font-semibold text-gray-900 mb-2">{t('hero.services.revenueGrowth.title')}</h3>
                  <p className="text-gray-600 text-sm">{t('hero.services.revenueGrowth.description')}</p>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
                  <div className="w-8 h-8 bg-nova-purple rounded-lg flex items-center justify-center mb-4">
                    <span className="text-white font-bold text-sm">24</span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">{t('hero.services.support24.title')}</h3>
                  <p className="text-gray-600 text-sm">{t('hero.services.support24.description')}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
