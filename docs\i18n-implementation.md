# Internationalization (i18n) Implementation

## Overview

This document describes the internationalization implementation for the Novalead website, supporting English, French, and Dutch languages.

## Technologies Used

- **react-i18next**: React integration for i18next
- **i18next**: Internationalization framework
- **i18next-browser-languagedetector**: Automatic language detection

## Project Structure

```
src/
├── i18n/
│   └── index.ts                 # i18n configuration
├── locales/
│   ├── en/
│   │   └── common.json         # English translations
│   ├── fr/
│   │   └── common.json         # French translations
│   └── nl/
│       └── common.json         # Dutch translations
├── components/
│   ├── LanguageSwitcher.tsx    # Language selection component
│   ├── Header.tsx              # Updated with translations
│   ├── Hero.tsx                # Updated with translations
│   ├── Services.tsx            # Updated with translations
│   ├── ServiceCard.tsx         # Updated with translations
│   ├── Contact.tsx             # Updated with translations
│   └── Footer.tsx              # Updated with translations
├── types/
│   └── i18n.ts                 # TypeScript types for translations
└── utils/
    └── testI18n.ts             # Testing utilities
```

## Features

### Language Support
- **English (en)**: Default language
- **French (fr)**: Complete translations
- **Dutch (nl)**: Complete translations

### Language Detection
- Automatically detects browser language
- Falls back to English if language not supported
- Stores language preference in localStorage

### Language Switcher
- Dropdown component with flag icons
- Available in both desktop and mobile navigation
- Smooth transitions and hover effects

## Usage

### Using Translations in Components

```tsx
import { useTranslation } from 'react-i18next';

const MyComponent = () => {
  const { t } = useTranslation();

  return (
    <h1>{t('hero.title.professional')}</h1>
  );
};
```

### Adding New Translations

1. Add the translation key to all language files:
   - `src/locales/en/common.json`
   - `src/locales/fr/common.json`
   - `src/locales/nl/common.json`

2. Update TypeScript types in `src/types/i18n.ts`

3. Use the translation in your component with `t('your.key')`

### Programmatic Language Change

```tsx
import { useTranslation } from 'react-i18next';

const { i18n } = useTranslation();
i18n.changeLanguage('fr'); // Switch to French
```

## Translation Keys Structure

### Navigation
- `navigation.home`
- `navigation.services`
- `navigation.contact`
- `navigation.getStarted`

### Hero Section
- `hero.title.professional`
- `hero.title.telemarketing`
- `hero.title.leadGeneration`
- `hero.description`
- `hero.buttons.getStartedToday`
- `hero.buttons.viewServices`
- `hero.stats.clientsReached.number`
- `hero.stats.clientsReached.label`
- `hero.stats.successRate.number`
- `hero.stats.successRate.label`
- `hero.stats.support.number`
- `hero.stats.support.label`
- `hero.services.expertCalling.title`
- `hero.services.expertCalling.description`
- `hero.services.leadQualification.title`
- `hero.services.leadQualification.description`
- `hero.services.revenueGrowth.title`
- `hero.services.revenueGrowth.description`
- `hero.services.support24.title`
- `hero.services.support24.description`

### Services Section
- `services.title.ourProfessional`
- `services.title.services`
- `services.description`
- `services.cta.title`
- `services.cta.description`
- `services.cta.buttons.getFreeConsultation`
- `services.cta.buttons.viewCaseStudies`

### Service Cards
- `serviceCard.keyBenefits`
- `serviceCard.learnMore`

### Contact Section
- `contact.title.getIn`
- `contact.title.touch`
- `contact.description`
- `contact.conversation.title`
- `contact.conversation.description`
- `contact.contactInfo.phone`
- `contact.contactInfo.email`
- `contact.contactInfo.office`
- `contact.businessHours.title`
- `contact.businessHours.mondayFriday`
- `contact.businessHours.saturday`
- `contact.businessHours.sunday`
- `contact.businessHours.closed`
- `contact.businessHours.timeWeekdays`
- `contact.businessHours.timeSaturday`
- `contact.form.fullName`
- `contact.form.emailAddress`
- `contact.form.companyName`
- `contact.form.message`
- `contact.form.placeholders.fullName`
- `contact.form.placeholders.email`
- `contact.form.placeholders.company`
- `contact.form.placeholders.message`
- `contact.form.sendMessage`
- `contact.form.success.title`
- `contact.form.success.description`

### Footer Section
- `footer.description`
- `footer.quickLinks.title`
- `footer.quickLinks.home`
- `footer.quickLinks.services`
- `footer.quickLinks.contact`
- `footer.quickLinks.aboutUs`
- `footer.quickLinks.caseStudies`
- `footer.ourServices.title`
- `footer.ourServices.leadGeneration`
- `footer.ourServices.crossSelling`
- `footer.ourServices.appointmentScheduling`
- `footer.ourServices.retentionCalls`
- `footer.ourServices.inboundTelemarketing`
- `footer.ourServices.customerResearch`
- `footer.ourServices.calendarManagement`
- `footer.legal.copyright`
- `footer.legal.privacyPolicy`
- `footer.legal.termsOfService`

### Services Data
- `servicesData.leadGeneration.title`
- `servicesData.leadGeneration.description`
- `servicesData.leadGeneration.benefits`
- `servicesData.crossSellingUpselling.title`
- `servicesData.crossSellingUpselling.description`
- `servicesData.crossSellingUpselling.benefits`
- `servicesData.appointmentScheduling.title`
- `servicesData.appointmentScheduling.description`
- `servicesData.appointmentScheduling.benefits`
- `servicesData.calendarManagement.title`
- `servicesData.calendarManagement.description`
- `servicesData.calendarManagement.benefits`
- `servicesData.retentionCalls.title`
- `servicesData.retentionCalls.description`
- `servicesData.retentionCalls.benefits`
- `servicesData.customerSatisfactionResearch.title`
- `servicesData.customerSatisfactionResearch.description`
- `servicesData.customerSatisfactionResearch.benefits`
- `servicesData.inboundTelemarketing.title`
- `servicesData.inboundTelemarketing.description`
- `servicesData.inboundTelemarketing.benefits`

## Error Handling

- **Missing translations**: Falls back to English
- **Invalid language codes**: Defaults to English
- **Network issues**: Uses cached translations

## Performance Considerations

- Translations are bundled with the application
- No runtime loading of translation files
- Minimal bundle size impact
- Efficient re-rendering with React.memo where needed

## Testing

Use the test utility to verify language switching:

```tsx
import { testLanguageSwitching } from '../utils/testI18n';

// In browser console or component
testLanguageSwitching();
```

## Browser Support

- Modern browsers with ES6+ support
- localStorage support required for language persistence
- Graceful degradation for older browsers

## Future Enhancements

1. **Additional Languages**: Easy to add more languages
2. **Lazy Loading**: Load translations on demand
3. **Pluralization**: Add support for complex plural rules
4. **Date/Number Formatting**: Locale-specific formatting
5. **RTL Support**: Right-to-left language support

## Maintenance

### Adding a New Language

1. Create new directory: `src/locales/{language-code}/`
2. Add `common.json` with all translation keys
3. Update `src/i18n/index.ts` to include new language
4. Add language option to `LanguageSwitcher.tsx`
5. Update TypeScript types if needed

### Updating Translations

1. Modify the appropriate JSON files
2. Ensure consistency across all languages
3. Test the changes in the browser
4. Verify no translation keys are missing

## Troubleshooting

### Common Issues

1. **Missing translations**: Check console for missing key warnings
2. **Language not switching**: Verify localStorage and browser settings
3. **TypeScript errors**: Ensure types are updated when adding new keys
4. **Layout issues**: Test with longer text in different languages

### Debug Mode

Enable debug mode in `src/i18n/index.ts`:

```tsx
i18n.init({
  // ...
  debug: true, // Enable for development
  // ...
});
```
