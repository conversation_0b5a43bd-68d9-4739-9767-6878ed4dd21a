// Simple utility to test i18n functionality
import i18n from '../i18n';

export const testLanguageSwitching = () => {
  console.log('Testing i18n functionality...');
  
  // Test English (default)
  i18n.changeLanguage('en');
  console.log('English title:', i18n.t('hero.title.professional'));
  
  // Test French
  i18n.changeLanguage('fr');
  console.log('French title:', i18n.t('hero.title.professional'));
  
  // Test Dutch
  i18n.changeLanguage('nl');
  console.log('Dutch title:', i18n.t('hero.title.professional'));
  
  // Reset to English
  i18n.changeLanguage('en');
  console.log('Reset to English');
};

// Export for debugging purposes
export { i18n };
